require('dotenv').config();
const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const url = require('url');
const { Pool } = require('pg');
const { v4: uuidv4 } = require('uuid');

// Redis for pub/sub (optional for multi-pod scaling)
let redisClient = null;
let redisSubscriber = null;
try {
  const Redis = require('ioredis');
  if (process.env.REDIS_URL) {
    redisClient = new Redis(process.env.REDIS_URL);
    redisSubscriber = new Redis(process.env.REDIS_URL);
    console.log('Redis client initialized for pub/sub');
  } else {
    console.log('Redis not configured, using single-pod mode');
  }
} catch (error) {
  console.log('Redis not available, using single-pod mode:', error.message);
}

// Determine if we're in test mode from environment variables
const isTestMode = process.env.TIPTOP_TEST_MODE === 'true' || process.env.NODE_ENV === 'development';
console.log(`WebSocket server running in ${isTestMode ? 'TEST' : 'PRODUCTION'} mode`);

// Create Express app
const app = express();
const server = http.createServer(app);

// Create WebSocket server
const wss = new WebSocket.Server({ server });

// Set up heartbeat interval to detect dead connections
const HEARTBEAT_INTERVAL = 30000; // 30 seconds
const heartbeatInterval = setInterval(() => {
  console.log('Sending heartbeat to all clients');
  let liveCount = 0;
  let deadCount = 0;

  wss.clients.forEach(client => {
    if (client.isAlive === false) {
      // Client didn't respond to the previous ping, terminate it
      console.log('Terminating dead connection');
      deadCount++;
      client.terminate();
      return;
    }

    // Client is alive, reset the flag and send a ping
    client.isAlive = false;
    liveCount++;
    client.ping(() => {});
  });

  console.log(`Heartbeat complete: ${liveCount} live connections, ${deadCount} dead connections terminated`);
}, HEARTBEAT_INTERVAL);

// PostgreSQL connection with optimized pooling
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'postgres',
  database: process.env.DB_NAME || 'tiptop',
  password: process.env.DB_PASSWORD || 'postgres',
  port: parseInt(process.env.DB_PORT || '5432'),
  // Connection pool optimization
  max: parseInt(process.env.DB_POOL_SIZE || '20'),     // Maximum number of clients in the pool
  idleTimeoutMillis: 30000,                            // Close idle connections after 30s
  connectionTimeoutMillis: 2000,                       // Connection timeout
  maxUses: 7500                                        // Close connections after 7500 queries
});

// Mock in-memory database for testing with memory limits
const inMemoryMessages = new Map();

// Configuration for memory limits
const MEMORY_LIMITS = {
  MAX_MESSAGES_PER_URL: parseInt(process.env.MAX_MESSAGES_PER_URL || '500'),  // Increased maximum messages stored per URL
  MAX_MESSAGE_SIZE_BYTES: parseInt(process.env.MAX_MESSAGE_SIZE_BYTES || '10000'),  // Maximum size of a single message (10KB)
  MAX_URLS_STORED: parseInt(process.env.MAX_URLS_STORED || '1000'),  // Maximum number of URLs to store messages for
  MESSAGE_EXPIRY_MS: parseInt(process.env.MESSAGE_EXPIRY_MS || '604800000'),  // Messages expire after 7 days
};

// Configuration for rate limiting
const RATE_LIMITS = {
  MESSAGES_PER_MINUTE: parseInt(process.env.RATE_LIMIT_MESSAGES_PER_MINUTE || '30'),  // Maximum messages per minute per client
  PRESENCE_UPDATES_PER_MINUTE: parseInt(process.env.RATE_LIMIT_PRESENCE_PER_MINUTE || '10'),  // Maximum presence updates per minute
  RATE_LIMIT_WINDOW_MS: 60000,  // 1 minute window for rate limiting
};

// Rate limiting tracker
const clientRateLimits = new Map(); // userId -> { messageTimestamps: [], presenceTimestamps: [] }

// Initialize database
async function initDatabase() {
  try {
    console.log('Initializing PostgreSQL database connection...');

    // Test the connection
    const client = await pool.connect();
    console.log('Database connection successful');

    // Create the tiptop_messages table if it doesn't exist
    await client.query(`
      CREATE TABLE IF NOT EXISTS tiptop_messages (
        id SERIAL PRIMARY KEY,
        type VARCHAR(50) NOT NULL,
        content TEXT NOT NULL,
        user_id VARCHAR(100) NOT NULL,
        user_name VARCHAR(100) NOT NULL,
        page_url TEXT NOT NULL,
        page_url_hash VARCHAR(100) NOT NULL,
        message_id VARCHAR(100) NOT NULL UNIQUE,
        timestamp TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create indexes for better performance
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_tiptop_messages_page_url_hash
      ON tiptop_messages(page_url_hash)
    `);

    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_tiptop_messages_timestamp
      ON tiptop_messages(timestamp)
    `);

    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_tiptop_messages_message_id
      ON tiptop_messages(message_id)
    `);

    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_tiptop_messages_created_at
      ON tiptop_messages(created_at)
    `);

    client.release();
    console.log('Database tables and indexes created successfully');
    return true;
  } catch (err) {
    console.error('Error initializing database:', err);
    console.error('Database connection details:', {
      user: process.env.DB_USER,
      host: process.env.DB_HOST,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT
    });
    return false;
  }
}

// Store active connections by URL
const rooms = new Map();

// Store user information for reconnection tracking
const userInfo = new Map(); // userId -> { userName, lastSeen, rooms: Set of URLs }

// Redis pub/sub setup
const REDIS_CHANNEL = 'tiptop_messages';

// Set up Redis subscriber if available
if (redisSubscriber) {
  redisSubscriber.subscribe(REDIS_CHANNEL);
  redisSubscriber.on('message', (channel, message) => {
    if (channel === REDIS_CHANNEL) {
      try {
        const messageData = JSON.parse(message);
        console.log('Received message from Redis:', messageData.type);

        // Broadcast to local clients only (avoid infinite loop)
        broadcastToLocalClients(messageData);
      } catch (error) {
        console.error('Error processing Redis message:', error);
      }
    }
  });
  console.log('Redis subscriber set up for channel:', REDIS_CHANNEL);
}

// Broadcast message to local clients only (for Redis pub/sub)
function broadcastToLocalClients(message) {
  const pageUrl = message.url;
  const room = rooms.get(pageUrl);

  if (!room) {
    console.log('No local room found for URL:', pageUrl);
    return;
  }

  console.log(`Broadcasting Redis message to ${room.size} local clients`);
  const messageJson = JSON.stringify(message);

  room.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      try {
        client.send(messageJson);
      } catch (error) {
        console.error('Error sending message to local client:', error);
      }
    }
  });
}

// Publish message to Redis for cross-pod communication
async function publishToRedis(message) {
  if (!redisClient) {
    return false;
  }

  try {
    await redisClient.publish(REDIS_CHANNEL, JSON.stringify(message));
    console.log('Message published to Redis');
    return true;
  } catch (error) {
    console.error('Error publishing to Redis:', error);
    return false;
  }
}

// Enhanced message broadcasting with error handling and retry logic
async function broadcastMessage(message, pageUrl, excludeClient = null) {
  console.log(`Broadcasting message type: ${message.type} to URL: ${pageUrl}`);

  // Add unique broadcast ID to prevent infinite loops in Redis
  const broadcastId = uuidv4();
  const enhancedMessage = {
    ...message,
    broadcastId,
    timestamp: message.timestamp || new Date().toISOString()
  };

  // First, publish to Redis for cross-pod communication
  if (redisClient) {
    try {
      await redisClient.publish(REDIS_CHANNEL, JSON.stringify(enhancedMessage));
      console.log('Message published to Redis for cross-pod broadcasting');
    } catch (error) {
      console.error('Error publishing to Redis:', error);
    }
  }

  // Then broadcast to local clients
  return broadcastToLocalClientsWithRetry(enhancedMessage, pageUrl, excludeClient);
}

// Broadcast to local clients with retry logic
async function broadcastToLocalClientsWithRetry(message, pageUrl, excludeClient = null, retryCount = 0) {
  const room = rooms.get(pageUrl);

  if (!room || room.size === 0) {
    console.log('No local clients found for URL:', pageUrl);
    return { success: true, sentCount: 0, totalClients: 0 };
  }

  console.log(`Broadcasting to ${room.size} local clients (retry: ${retryCount})`);

  const messageJson = JSON.stringify(message);
  const clients = Array.from(room);
  const totalClients = clients.length;
  let sentCount = 0;
  let failedClients = [];

  // Send to all clients
  for (const client of clients) {
    // Skip excluded client (e.g., sender)
    if (excludeClient && client === excludeClient) {
      continue;
    }

    if (client.readyState === WebSocket.OPEN) {
      try {
        client.send(messageJson);
        sentCount++;
      } catch (error) {
        console.error('Error sending message to client:', error);
        failedClients.push(client);
      }
    } else {
      // Remove dead connections
      room.delete(client);
      console.log('Removed dead connection from room');
    }
  }

  console.log(`Message sent to ${sentCount}/${totalClients} local clients`);

  // Retry failed clients once
  if (failedClients.length > 0 && retryCount === 0) {
    console.log(`Retrying ${failedClients.length} failed clients`);
    setTimeout(() => {
      failedClients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
          try {
            client.send(messageJson);
            sentCount++;
          } catch (error) {
            console.error('Retry failed for client:', error);
            // Remove persistently failing client
            room.delete(client);
          }
        }
      });
    }, 1000);
  }

  return { success: true, sentCount, totalClients };
}

// Function to create a consistent URL hash
function createUrlHash(url) {
  // Normalize the URL by removing trailing slashes, query parameters, etc.
  let normalizedUrl = url;
  try {
    const urlObj = new URL(url);
    // Use only the origin and pathname for hashing
    normalizedUrl = urlObj.origin + urlObj.pathname;
  } catch (e) {
    console.error('Error normalizing URL:', e);
    // Continue with the original URL if parsing fails
  }

  console.log(`Normalized URL for hashing: ${normalizedUrl}`);
  return Buffer.from(normalizedUrl).toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
}

// Function to save message to PostgreSQL database
async function saveMessage(message) {
  try {
    console.log('Saving message to PostgreSQL database:', message);
    const pageUrlHash = createUrlHash(message.url);
    console.log(`URL hash for saving: ${pageUrlHash}`);

    // Check message size limit
    const MAX_MESSAGE_SIZE = 10000; // 10KB limit
    if (message.content && message.content.length > MAX_MESSAGE_SIZE) {
      console.warn(`Message content too large (${message.content.length} chars), truncating to ${MAX_MESSAGE_SIZE} chars`);
      message.content = message.content.substring(0, MAX_MESSAGE_SIZE) + '... [truncated]';
    }

    const timestamp = new Date(message.timestamp);
    console.log(`Parsed timestamp: ${timestamp.toISOString()}`);

    // Insert message into PostgreSQL database
    const client = await pool.connect();
    try {
      const result = await client.query(`
        INSERT INTO tiptop_messages (
          type, content, user_id, user_name, page_url,
          page_url_hash, message_id, timestamp
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id
      `, [
        message.type,
        message.content,
        message.userId,
        message.userName,
        message.url,
        pageUrlHash,
        message.messageId,
        timestamp
      ]);

      const savedId = result.rows[0].id;
      console.log(`Message saved to PostgreSQL with ID: ${savedId}`);
      console.log(`Message content: "${message.content.substring(0, 30)}${message.content.length > 30 ? '...' : ''}"`);
      console.log(`Message URL: ${message.url}`);
      console.log(`Message URL hash: ${pageUrlHash}`);

      return savedId;
    } finally {
      client.release();
    }
  } catch (err) {
    console.error('Error saving message to PostgreSQL database:', err);
    console.error(err.stack);
    return null;
  }
}



// Function to get recent messages for a URL from PostgreSQL database
async function getRecentMessages(pageUrl, limit = 500, lastMessageId = null) {
  try {
    console.log(`Getting recent messages for URL: ${pageUrl}, limit: ${limit}, lastMessageId: ${lastMessageId}`);
    const pageUrlHash = createUrlHash(pageUrl);
    console.log(`URL hash: ${pageUrlHash}`);

    const client = await pool.connect();
    try {
      let query;
      let params;

      if (lastMessageId) {
        // Get messages after the specified message ID
        query = `
          SELECT type, content, user_id, user_name, page_url, message_id, timestamp, created_at
          FROM tiptop_messages
          WHERE page_url_hash = $1 AND id > $2
          ORDER BY created_at ASC
          LIMIT $3
        `;
        params = [pageUrlHash, lastMessageId, limit];
      } else {
        // Get the most recent messages
        query = `
          SELECT type, content, user_id, user_name, page_url, message_id, timestamp, created_at
          FROM tiptop_messages
          WHERE page_url_hash = $1
          ORDER BY created_at DESC
          LIMIT $2
        `;
        params = [pageUrlHash, limit];
      }

      const result = await client.query(query, params);
      console.log(`PostgreSQL returned ${result.rows.length} messages`);

      // If we got recent messages (not incremental), reverse to get chronological order
      const rows = lastMessageId ? result.rows : result.rows.reverse();

      // Map to client format
      const messages = rows.map(row => ({
        type: row.type,
        content: row.content,
        userId: row.user_id,
        userName: row.user_name,
        url: row.page_url,
        messageId: row.message_id,
        timestamp: row.timestamp.toISOString(),
        serverTimestamp: row.created_at.toISOString()
      }));

      console.log(`Mapped ${messages.length} messages for client`);
      return messages;
    } finally {
      client.release();
    }
  } catch (err) {
    console.error('Error getting recent messages:', err);
    console.error(err.stack);
    return [];
  }
}

// Function to get active users in a room
function getActiveUsers(roomUrl) {
  const room = rooms.get(roomUrl);
  if (!room) return [];

  // Collect unique user IDs and names from the room
  const users = new Map();

  // First, add all users with active connections in this room
  room.forEach(client => {
    if (client.userId && client.userName) {
      users.set(client.userId, {
        userId: client.userId,
        userName: client.userName
      });
    }
  });

  // Log the number of users found in the room
  console.log(`Found ${users.size} users with active connections in room ${roomUrl}`);

  // For debugging, log all users in the room
  if (users.size > 0) {
    console.log('Users in room:', Array.from(users.values()).map(u => `${u.userName} (${u.userId})`));
  }

  return Array.from(users.values());
}

// WebSocket connection handler
wss.on('connection', async (ws, req) => {
  const parameters = url.parse(req.url, true);
  let pageUrl = parameters.query.url;
  const userId = parameters.query.userId;
  const userName = parameters.query.userName;

  console.log('New WebSocket connection with URL parameter:', pageUrl);
  console.log('User ID:', userId, 'User Name:', userName);

  if (!pageUrl) {
    console.error('No URL parameter provided');
    ws.close(1003, 'URL parameter required');
    return;
  }

  // Try to decode the URL if it's encoded
  try {
    pageUrl = decodeURIComponent(pageUrl);
    console.log('Decoded URL:', pageUrl);
  } catch (e) {
    console.error('Error decoding URL:', e);
    // Continue with the original URL if decoding fails
  }

  // Store user info on the WebSocket connection
  ws.userId = userId;
  ws.userName = userName;
  ws.pageUrl = pageUrl;

  // Add user to the room for this URL
  if (!rooms.has(pageUrl)) {
    rooms.set(pageUrl, new Set());
  }
  rooms.get(pageUrl).add(ws);

  console.log(`Client connected to room: ${pageUrl}`);

  // Set up heartbeat handling
  ws.isAlive = true;
  ws.on('pong', () => {
    ws.isAlive = true;
    console.log(`Received pong from client in room: ${pageUrl}`);
  });

  // Send recent messages from database (initial load)
  try {
    console.log(`Retrieving history messages for URL: ${pageUrl}`);
    const recentMessages = await getRecentMessages(pageUrl, 100); // Limit initial load
    console.log(`Retrieved ${recentMessages.length} history messages to send to new client`);

    // Log the first few messages for debugging
    if (recentMessages.length > 0) {
      console.log('First message:', JSON.stringify(recentMessages[0]));
      if (recentMessages.length > 1) {
        console.log('Second message:', JSON.stringify(recentMessages[1]));
      }
    }

    if (recentMessages.length > 0) {
      // No system message for history loading - we'll just send the history directly
      console.log(`Preparing to send ${recentMessages.length} history messages`);

      // Send history messages in batches to ensure reliable delivery
      const BATCH_SIZE = 50; // Send 50 messages at a time
      for (let i = 0; i < recentMessages.length; i += BATCH_SIZE) {
        const batch = recentMessages.slice(i, i + BATCH_SIZE);
        const batchMessage = {
          type: 'history_batch',
          batchIndex: Math.floor(i / BATCH_SIZE) + 1,
          totalBatches: Math.ceil(recentMessages.length / BATCH_SIZE),
          messages: batch,
          timestamp: new Date().toISOString()
        };
        const messageJson = JSON.stringify(batchMessage);
        console.log(`Sending history batch ${batchMessage.batchIndex}/${batchMessage.totalBatches} with ${batch.length} messages`);
        ws.send(messageJson);
        // Small delay between batches to prevent overwhelming the client
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      console.log('All history batches sent successfully');
    } else {
      console.log('No history messages to send');
    }
  } catch (err) {
    console.error('Error sending message history:', err);
    console.error(err.stack);
  }

  // Handle incoming messages
  ws.on('message', async (message) => {
    try {
      console.log(`Received message from client: ${message.toString().substring(0, 100)}...`);
      const parsedMessage = JSON.parse(message.toString());
      console.log(`Parsed message type: ${parsedMessage.type}`);

      // Add server timestamp
      parsedMessage.serverTimestamp = new Date().toISOString();

      // Handle presence updates
      if (parsedMessage.type === 'presence') {
        console.log(`Presence update from user: ${parsedMessage.userName} (${parsedMessage.userId})`);

        // Check rate limiting for presence updates
        if (parsedMessage.userId && isRateLimited(parsedMessage.userId, 'presence')) {
          console.warn(`Rate limiting presence update from ${parsedMessage.userName} (${parsedMessage.userId})`);
          try {
            ws.send(JSON.stringify({
              type: 'error',
              error: 'Rate limit exceeded for presence updates. Please try again later.',
              code: 'RATE_LIMIT_EXCEEDED',
              timestamp: new Date().toISOString()
            }));
          } catch (error) {
            console.error('Error sending rate limit message:', error);
          }
          return; // Skip processing this message
        }

        // Store user info with the connection
        ws.userId = parsedMessage.userId;
        ws.userName = parsedMessage.userName;

        // Update global user info for reconnection tracking
        if (!userInfo.has(parsedMessage.userId)) {
          userInfo.set(parsedMessage.userId, {
            userName: parsedMessage.userName,
            lastSeen: new Date(),
            rooms: new Set([pageUrl])
          });
          console.log(`Added new user to tracking: ${parsedMessage.userName} (${parsedMessage.userId})`);
        } else {
          // Update existing user info
          const info = userInfo.get(parsedMessage.userId);
          info.userName = parsedMessage.userName; // Update name in case it changed
          info.lastSeen = new Date();
          info.rooms.add(pageUrl);
          console.log(`Updated existing user: ${parsedMessage.userName} (${parsedMessage.userId})`);
          console.log(`User is now in ${info.rooms.size} rooms`);
        }

        // Broadcast updated user list to all clients in the room
        const activeUsers = getActiveUsers(pageUrl);
        console.log(`Active users in room ${pageUrl}: ${activeUsers.length}`);
        const room = rooms.get(pageUrl);
        if (room) {
          console.log(`Broadcasting user list to ${room.size} clients`);

          // Prepare the user list message once
          const userListMessage = JSON.stringify({
            type: 'users',
            users: activeUsers,
            timestamp: new Date().toISOString()
          });

          // Use batching for large rooms
          const clients = Array.from(room);
          const totalClients = clients.length;
          const BATCH_SIZE = 25; // Process 25 clients at a time

          // Function to process a batch of clients
          const processBatch = (startIndex) => {
            const endIndex = Math.min(startIndex + BATCH_SIZE, totalClients);

            for (let i = startIndex; i < endIndex; i++) {
              const client = clients[i];
              if (client.readyState === WebSocket.OPEN) {
                try {
                  client.send(userListMessage);
                } catch (sendErr) {
                  console.error('Error sending user list to client:', sendErr);
                }
              }
            }

            // If there are more clients to process, schedule the next batch
            if (endIndex < totalClients) {
              setImmediate(() => processBatch(endIndex));
            }
          };

          // Start processing the first batch
          processBatch(0);
        }
      }

      // Handle request_scroll messages
      else if (parsedMessage.type === 'request_scroll') {
        console.log(`Received request_scroll from client for URL: ${pageUrl}`);

        // Get the sender's ID for the message
        const senderId = ws.userId || 'unknown';

        // Send scroll command to all clients in the room with enhanced information
        const room = rooms.get(pageUrl);
        if (room) {
          console.log(`Sending enhanced scroll command to ${room.size} clients in room`);

          // Send multiple scroll commands with increasing delays
          // This ensures clients receive scroll commands at different times,
          // increasing the chance of successful scrolling
          const scrollDelays = [100, 300, 600, 1000];

          scrollDelays.forEach((delay, index) => {
            setTimeout(() => {
              let sentCount = 0;

              room.forEach((client) => {
                if (client.readyState === WebSocket.OPEN) {
                  try {
                    client.send(JSON.stringify({
                      type: 'scroll_chat',
                      timestamp: new Date().toISOString(),
                      senderId: senderId,
                      forceScroll: true, // Force scroll for everyone to ensure visibility
                      messageId: parsedMessage.messageId || null,
                      attempt: index + 1,
                      totalAttempts: scrollDelays.length
                    }));
                    sentCount++;
                  } catch (sendErr) {
                    console.error('Error sending scroll command to client:', sendErr);
                  }
                }
              });

              console.log(`Enhanced scroll command (attempt ${index + 1}/${scrollDelays.length}) sent to ${sentCount} clients after ${delay}ms delay`);
            }, delay);
          });
        }
      }

      // Handle get_users requests
      else if (parsedMessage.type === 'get_users') {
        console.log(`Received get_users request from client for URL: ${pageUrl}`);
        console.log(`Request from user: ${ws.userName || 'Unknown'} (${ws.userId || 'Unknown ID'})`);

        // Get active users for this room
        const activeUsers = getActiveUsers(pageUrl);
        console.log(`Active users in room ${pageUrl}: ${activeUsers.length}`);

        // Log room size and connection details
        const room = rooms.get(pageUrl);
        if (room) {
          console.log(`Room size: ${room.size} connections`);
          let openCount = 0;
          let closingCount = 0;
          let closedCount = 0;
          let connectingCount = 0;

          room.forEach(client => {
            if (client.readyState === WebSocket.OPEN) openCount++;
            else if (client.readyState === WebSocket.CLOSING) closingCount++;
            else if (client.readyState === WebSocket.CLOSED) closedCount++;
            else if (client.readyState === WebSocket.CONNECTING) connectingCount++;
          });

          console.log(`Connection states in room: Open=${openCount}, Connecting=${connectingCount}, Closing=${closingCount}, Closed=${closedCount}`);
        } else {
          console.log('Room not found for this URL');
        }

        // Send the user list back to the requesting client only
        try {
          const response = {
            type: 'users',
            users: activeUsers,
            timestamp: new Date().toISOString()
          };

          const responseJson = JSON.stringify(response);
          ws.send(responseJson);

          console.log(`Sent user list with ${activeUsers.length} users to requesting client`);
          if (activeUsers.length > 0) {
            console.log('User list sent:', activeUsers.map(u => `${u.userName} (${u.userId})`));
          }
        } catch (error) {
          console.error('Error sending user list to client:', error);
        }
      }

      // Handle get_history requests
      else if (parsedMessage.type === 'get_history') {
        console.log(`Received get_history request from client for URL: ${pageUrl}`);
        console.log(`Request from user: ${ws.userName || 'Unknown'} (${ws.userId || 'Unknown ID'})`);
        console.log(`Last message ID: ${parsedMessage.lastMessageId || 'none'}`);

        // Get recent messages for this URL
        try {
          const lastMessageId = parsedMessage.lastMessageId || null;
          const limit = parsedMessage.limit || 100;

          const recentMessages = await getRecentMessages(pageUrl, limit, lastMessageId);
          console.log(`Retrieved ${recentMessages.length} history messages to send to client`);

          if (recentMessages.length > 0) {
            // Send history response with all messages
            const historyResponse = {
              type: 'history',
              url: pageUrl,
              messages: recentMessages,
              lastMessageId: lastMessageId,
              timestamp: new Date().toISOString()
            };

            ws.send(JSON.stringify(historyResponse));
            console.log(`Sent history with ${recentMessages.length} messages to requesting client`);
          } else {
            console.log('No history messages found for this URL');
            // Send empty history response
            const historyResponse = {
              type: 'history',
              url: pageUrl,
              messages: [],
              lastMessageId: lastMessageId,
              timestamp: new Date().toISOString()
            };
            ws.send(JSON.stringify(historyResponse));
          }
        } catch (error) {
          console.error('Error retrieving history messages:', error);
          // Send error response
          ws.send(JSON.stringify({
            type: 'error',
            error: 'Failed to retrieve message history',
            code: 'HISTORY_ERROR',
            timestamp: new Date().toISOString()
          }));
        }
      }

      // Save message to database (except presence updates)
      if (parsedMessage.type === 'chat' || parsedMessage.type === 'note') {
        // Check rate limiting for chat/note messages
        if (parsedMessage.userId && isRateLimited(parsedMessage.userId, parsedMessage.type)) {
          console.warn(`Rate limiting ${parsedMessage.type} message from ${parsedMessage.userName} (${parsedMessage.userId})`);
          try {
            ws.send(JSON.stringify({
              type: 'error',
              error: `Rate limit exceeded for ${parsedMessage.type} messages. Please try again later.`,
              code: 'RATE_LIMIT_EXCEEDED',
              timestamp: new Date().toISOString()
            }));
          } catch (error) {
            console.error('Error sending rate limit message:', error);
          }
          return; // Skip processing this message
        }

        console.log(`Saving ${parsedMessage.type} message to database`);
        const savedId = await saveMessage(parsedMessage);
        console.log(`Message saved with ID: ${savedId}`);
      }

      // Broadcast to all clients using enhanced broadcasting
      try {
        const result = await broadcastMessage(parsedMessage, pageUrl, ws);
        console.log(`Message broadcast result: ${result.sentCount}/${result.totalClients} clients reached`);

        // For chat messages, also send a scroll command
        if (parsedMessage.type === 'chat') {
          console.log('Sending scroll command to all clients');

          const scrollCommand = {
            type: 'scroll_chat',
            timestamp: new Date().toISOString(),
            senderId: ws.userId || 'unknown',
            messageId: parsedMessage.messageId || null
          };

          // Send scroll command using the same broadcasting mechanism
          setTimeout(async () => {
            try {
              await broadcastMessage(scrollCommand, pageUrl);
              console.log('Scroll command sent to all clients');
            } catch (error) {
              console.error('Error sending scroll command:', error);
            }
          }, 200);
        }
      } catch (error) {
        console.error('Error broadcasting message:', error);
      }
    } catch (e) {
      console.error('Error processing message:', e);
      console.error(e.stack);
    }
  });

  // Handle disconnection
  ws.on('close', () => {
    console.log(`Client disconnected from room: ${pageUrl}`);
    if (ws.userId) {
      console.log(`Disconnected user: ${ws.userName || 'Unknown'} (${ws.userId})`);
    }

    // Remove from room
    const room = rooms.get(pageUrl);
    if (room) {
      // Check if this user has other connections in the same room
      let hasOtherConnections = false;
      const userId = ws.userId;

      if (userId) {
        room.forEach(client => {
          if (client !== ws && client.userId === userId && client.readyState === WebSocket.OPEN) {
            hasOtherConnections = true;
            console.log(`User ${userId} has other active connections in the same room`);
          }
        });
      }

      // Remove this specific connection
      room.delete(ws);
      console.log(`Removed connection from room. New room size: ${room.size}`);

      // Broadcast updated user list only if this was the user's last connection
      if (ws.userId && !hasOtherConnections) {
        console.log(`Broadcasting user list update after ${ws.userName || 'Unknown'} (${ws.userId}) disconnected`);
        const activeUsers = getActiveUsers(pageUrl);

        // Update user info tracking
        if (userInfo.has(ws.userId)) {
          const info = userInfo.get(ws.userId);
          info.rooms.delete(pageUrl);
          console.log(`Updated user tracking: ${ws.userId} removed from room ${pageUrl}`);
          console.log(`User is now in ${info.rooms.size} rooms`);
        }

        // Send updated user list to all clients in the room
        room.forEach((client) => {
          if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
              type: 'users',
              users: activeUsers,
              timestamp: new Date().toISOString()
            }));
          }
        });
      }

      // Clean up empty rooms
      if (room.size === 0) {
        console.log(`Room ${pageUrl} is now empty, removing it`);
        rooms.delete(pageUrl);
      }
    } else {
      console.log(`Room not found for URL: ${pageUrl}`);
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).send('OK');
});

// Status endpoint with enhanced monitoring information
app.get('/status', (req, res) => {
  // Count connections by state
  let openConnections = 0;
  let closingConnections = 0;
  let closedConnections = 0;
  let connectingConnections = 0;
  let totalConnections = 0;

  // Count users and messages
  const activeUsers = new Set();
  let totalMessages = 0;

  // Gather room statistics
  const roomStats = [];

  rooms.forEach((room, url) => {
    let roomOpenConnections = 0;
    let roomClosingConnections = 0;
    let roomClosedConnections = 0;
    let roomConnectingConnections = 0;

    room.forEach(client => {
      totalConnections++;

      if (client.readyState === WebSocket.OPEN) {
        openConnections++;
        roomOpenConnections++;
      } else if (client.readyState === WebSocket.CLOSING) {
        closingConnections++;
        roomClosingConnections++;
      } else if (client.readyState === WebSocket.CLOSED) {
        closedConnections++;
        roomClosedConnections++;
      } else if (client.readyState === WebSocket.CONNECTING) {
        connectingConnections++;
        roomConnectingConnections++;
      }

      if (client.userId) {
        activeUsers.add(client.userId);
      }
    });

    // Get message count for this URL
    const urlHash = createUrlHash(url);
    const messagesForUrl = inMemoryMessages.get(urlHash) || [];
    totalMessages += messagesForUrl.length;

    // Add room statistics
    roomStats.push({
      url: url.substring(0, 100) + (url.length > 100 ? '...' : ''), // Truncate long URLs
      connections: room.size,
      openConnections: roomOpenConnections,
      messages: messagesForUrl.length
    });
  });

  // Sort room stats by connection count (descending)
  roomStats.sort((a, b) => b.connections - a.connections);

  // Limit to top 10 rooms
  const topRooms = roomStats.slice(0, 10);

  // Get rate limit statistics
  const rateLimitedUsers = clientRateLimits.size;

  // Create the status object
  const status = {
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    memory: {
      heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024 * 100) / 100 + ' MB',
      heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024 * 100) / 100 + ' MB',
      rss: Math.round(process.memoryUsage().rss / 1024 / 1024 * 100) / 100 + ' MB'
    },
    connections: {
      total: totalConnections,
      open: openConnections,
      closing: closingConnections,
      closed: closedConnections,
      connecting: connectingConnections
    },
    rooms: {
      count: rooms.size,
      topRooms: topRooms
    },
    users: {
      count: activeUsers.size,
      rateLimited: rateLimitedUsers
    },
    messages: {
      total: totalMessages,
      urlsTracked: inMemoryMessages.size
    },
    config: {
      maxMessagesPerUrl: MEMORY_LIMITS.MAX_MESSAGES_PER_URL,
      messageExpiryHours: Math.round(MEMORY_LIMITS.MESSAGE_EXPIRY_MS / 1000 / 60 / 60),
      rateLimit: {
        messagesPerMinute: RATE_LIMITS.MESSAGES_PER_MINUTE,
        presenceUpdatesPerMinute: RATE_LIMITS.PRESENCE_UPDATES_PER_MINUTE
      }
    }
  };

  res.status(200).json(status);
});

// Function to insert a test message (for debugging)
async function insertTestMessage() {
  try {
    const testUrl = 'https://example.com';
    const testUrlHash = createUrlHash(testUrl);

    console.log('Inserting test message with URL hash:', testUrlHash);

    // Create test message
    const message = {
      type: 'chat',
      content: 'This is a test message',
      userId: 'test-user-id',
      userName: 'Test User',
      url: testUrl,
      messageId: `test-msg-${Date.now()}`,
      timestamp: new Date().toISOString()
    };

    // Save to in-memory database
    await saveMessage(message);

    console.log('Test message inserted successfully');

    // Insert a second test message
    const message2 = {
      type: 'chat',
      content: 'This is another test message',
      userId: 'test-user-id-2',
      userName: 'Another Test User',
      url: testUrl,
      messageId: `test-msg-${Date.now() + 1}`,
      timestamp: new Date().toISOString()
    };

    await saveMessage(message2);

    // Verify the messages were inserted
    const messages = await getRecentMessages(testUrl, 10);
    console.log(`Retrieved ${messages.length} messages for test URL`);
  } catch (err) {
    console.error('Error inserting test message:', err);
  }
}

// Function to check if a client is rate limited
function isRateLimited(userId, messageType) {
  if (!userId) return false; // Skip rate limiting for users without IDs

  // Initialize rate limit tracking for this user if it doesn't exist
  if (!clientRateLimits.has(userId)) {
    clientRateLimits.set(userId, {
      messageTimestamps: [],
      presenceTimestamps: []
    });
  }

  const userRateLimits = clientRateLimits.get(userId);
  const now = Date.now();
  const windowStart = now - RATE_LIMITS.RATE_LIMIT_WINDOW_MS;

  // Clean up old timestamps outside the window
  if (messageType === 'chat' || messageType === 'note') {
    userRateLimits.messageTimestamps = userRateLimits.messageTimestamps.filter(
      timestamp => timestamp > windowStart
    );
  } else if (messageType === 'presence') {
    userRateLimits.presenceTimestamps = userRateLimits.presenceTimestamps.filter(
      timestamp => timestamp > windowStart
    );
  }

  // Check if user is over the limit
  if (messageType === 'chat' || messageType === 'note') {
    if (userRateLimits.messageTimestamps.length >= RATE_LIMITS.MESSAGES_PER_MINUTE) {
      console.warn(`User ${userId} is rate limited for messages: ${userRateLimits.messageTimestamps.length} messages in the last minute`);
      return true;
    }
    // Add current timestamp for this message
    userRateLimits.messageTimestamps.push(now);
  } else if (messageType === 'presence') {
    if (userRateLimits.presenceTimestamps.length >= RATE_LIMITS.PRESENCE_UPDATES_PER_MINUTE) {
      console.warn(`User ${userId} is rate limited for presence updates: ${userRateLimits.presenceTimestamps.length} updates in the last minute`);
      return true;
    }
    // Add current timestamp for this presence update
    userRateLimits.presenceTimestamps.push(now);
  }

  return false;
}

// Function to clean up stale connections and rate limit data
function cleanupStaleConnections() {
  console.log('Running periodic cleanup of stale connections and rate limit data');

  let totalConnections = 0;
  let closedConnections = 0;

  // Check each room
  rooms.forEach((room, url) => {
    console.log(`Checking room ${url} with ${room.size} connections`);
    totalConnections += room.size;

    // Find closed connections
    const staleConnections = [];
    room.forEach(client => {
      if (client.readyState === WebSocket.CLOSED || client.readyState === WebSocket.CLOSING) {
        staleConnections.push(client);
      }
    });

    // Remove stale connections
    if (staleConnections.length > 0) {
      console.log(`Removing ${staleConnections.length} stale connections from room ${url}`);
      staleConnections.forEach(client => {
        room.delete(client);
        closedConnections++;
      });

      // If room is now empty, remove it
      if (room.size === 0) {
        console.log(`Room ${url} is now empty after cleanup, removing it`);
        rooms.delete(url);
      } else {
        // Broadcast updated user list
        const activeUsers = getActiveUsers(url);
        room.forEach(client => {
          if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
              type: 'users',
              users: activeUsers,
              timestamp: new Date().toISOString()
            }));
          }
        });
      }
    }
  });

  // Clean up rate limit data for users who are no longer connected
  const now = Date.now();
  const activeUserIds = new Set();

  // Collect all active user IDs
  rooms.forEach(room => {
    room.forEach(client => {
      if (client.userId) {
        activeUserIds.add(client.userId);
      }
    });
  });

  // Remove rate limit data for inactive users
  let removedRateLimitEntries = 0;
  clientRateLimits.forEach((data, userId) => {
    if (!activeUserIds.has(userId)) {
      clientRateLimits.delete(userId);
      removedRateLimitEntries++;
    }
  });

  if (removedRateLimitEntries > 0) {
    console.log(`Removed rate limit data for ${removedRateLimitEntries} inactive users`);
  }

  console.log(`Cleanup complete: Checked ${totalConnections} connections, removed ${closedConnections} stale connections`);
  console.log(`Remaining rooms: ${rooms.size}, Rate limited users: ${clientRateLimits.size}`);
}

// Start the server
const PORT = process.env.PORT || 8080;
server.listen(PORT, async () => {
  console.log(`WebSocket server listening on port ${PORT}`);
  await initDatabase();

  // Insert a test message if DEBUG is enabled
  if (process.env.DEBUG === 'true') {
    console.log('DEBUG mode enabled, inserting test message');
    await insertTestMessage();
  }

  // Set up periodic cleanup of stale connections
  setInterval(cleanupStaleConnections, 60000); // Run every minute
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');

  // Clear the heartbeat interval
  clearInterval(heartbeatInterval);
  console.log('Heartbeat interval cleared');

  // Close all WebSocket connections
  wss.clients.forEach(client => {
    client.terminate();
  });
  console.log('All WebSocket connections terminated');

  server.close(() => {
    console.log('HTTP server closed');
    pool.end(() => {
      console.log('Database connection pool closed');
      process.exit(0);
    });
  });
});
